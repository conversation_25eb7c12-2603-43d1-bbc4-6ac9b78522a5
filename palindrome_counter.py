import sys

def solve():
    """
    解决美团笔试题：最小化数组极差的操作次数。
    """
    try:
        # 读取数组长度 n
        n_str = sys.stdin.readline()
        if not n_str: return # 处理文件末尾空行
        n = int(n_str)
        
        # 如果数组为空或只有一个元素，不需要操作
        if n <= 1:
            if n == 1:
                # 读取这一行数组数据，但不用它
                sys.stdin.readline()
            print(0)
            return
            
        # 读取数组
        a = list(map(int, sys.stdin.readline().split()))

    except (IOError, ValueError):
        # 处理可能的输入错误
        return

    # 初始化最长同值子数组的长度
    max_len = 0
    # 如果数组不为空，至少有一个元素，所以初始长度至少为1
    if n > 0:
        max_len = 1
    
    current_len = 1
    # 从第二个元素开始遍历
    for i in range(1, n):
        # 如果当前元素和前一个相同，增加当前连续长度
        if a[i] == a[i-1]:
            current_len += 1
        else:
            # 如果不同，说明一个连续块结束了
            # 更新全局最长长度
            max_len = max(max_len, current_len)
            # 为新元素重置当前连续长度为1
            current_len = 1
            
    # 循环结束后，别忘了最后更新一次 max_len，以考虑数组末尾的连续块
    max_len = max(max_len, current_len)
    
    # 最少操作数 = 总长度 - 需要保留的最长子数组长度
    min_ops = n - max_len
    print(min_ops)


def main():
    """
    主函数，处理多组测试数据。
    """
    try:
        # 读取测试数据组数 T
        t_str = sys.stdin.readline()
        if not t_str: return
        t = int(t_str)
        
        for _ in range(t):
            solve()
    except (IOError, ValueError):
        return

if __name__ == "__main__":
    main()