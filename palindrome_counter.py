import sys
from collections import defaultdict, deque

def longest_path_through_two_nodes(n, edges, queries):

    graph = defaultdict(list)
    for u, v, w in edges:
        graph[u].append((v, w))
        graph[v].append((u, w))


    def calc_distances(start):
        distances = [-1] * (n + 1)
        distances[start] = 0
        queue = deque([start])
        
        while queue:
            node = queue.popleft()
            for neighbor, weight in graph[node]:
                if distances[neighbor] == -1:
                    distances[neighbor] = distances[node] + weight
                    queue.append(neighbor)
        
        # 找到最远的节点
        max_dist = -1
        farthest = -1
        for i in range(1, n + 1):
            if distances[i] > max_dist:
                max_dist = distances[i]
                farthest = i
        
        return farthest, distances
    
    # 对每个查询计算结果
    results = []
    for x, y in queries:
        # 如果x和y相同，则找出以x为端点的最长路径
        if x == y:
            # 找到从x出发最远的点u
            u, _ = calc_distances(x)
            # 找到从u出发最远的点v
            v, dist_from_u = calc_distances(u)
            # u到v的路径就是树的直径
            results.append(dist_from_u[v])
            continue
        
        # 首先计算x和y之间的路径
        # 这里使用BFS找到x到y的最短路径
        queue = deque([x])
        distances = [-1] * (n + 1)
        distances[x] = 0
        parents = [-1] * (n + 1)
        
        while queue and distances[y] == -1:
            node = queue.popleft()
            for neighbor, weight in graph[node]:
                if distances[neighbor] == -1:
                    distances[neighbor] = distances[node] + weight
                    parents[neighbor] = node
                    queue.append(neighbor)
        
        # 获取x到y的路径上的所有节点
        path_nodes = set()
        current = y
        while current != -1:
            path_nodes.add(current)
            current = parents[current]
            
        # 找到经过x和y的最长路径
        # 方法：在树中找出一条最长的路径，使得该路径经过x和y
        
        # 1. 从x出发，找到除y及x到y路径上节点外的最远节点a
        max_dist_a = -1
        node_a = -1
        
        # 这里使用DFS从x出发，避开x到y路径上的节点
        def dfs_from_x(node, parent, dist):
            nonlocal max_dist_a, node_a
            
            # 如果当前节点在x到y的路径上（除了x自己），则不继续
            if node != x and node in path_nodes:
                return
                
            if dist > max_dist_a:
                max_dist_a = dist
                node_a = node
                
            for neighbor, weight in graph[node]:
                if neighbor != parent:
                    dfs_from_x(neighbor, node, dist + weight)
        
        dfs_from_x(x, -1, 0)
        
        # 2. 从y出发，找到除x及x到y路径上节点外的最远节点b
        max_dist_b = -1
        node_b = -1
        
        def dfs_from_y(node, parent, dist):
            nonlocal max_dist_b, node_b
            
            # 如果当前节点在x到y的路径上（除了y自己），则不继续
            if node != y and node in path_nodes:
                return
                
            if dist > max_dist_b:
                max_dist_b = dist
                node_b = node
                
            for neighbor, weight in graph[node]:
                if neighbor != parent:
                    dfs_from_y(neighbor, node, dist + weight)
        
        dfs_from_y(y, -1, 0)

        xy_distance = distances[y]
        

        if node_a != -1 and node_b != -1:

            longest_path = max_dist_a + xy_distance + max_dist_b

        elif node_a != -1:

            _, dist_a = calc_distances(node_a)
            longest_path = dist_a[y]

        elif node_b != -1:

            _, dist_b = calc_distances(node_b)
            longest_path = dist_b[x]

        else:
            longest_path = xy_distance
            
        results.append(longest_path)
    
    return results

if __name__ == "__main__":
    try:
        # 读取输入
        n, m = map(int, input().strip().split())
        
        edges = []
        for _ in range(n - 1):
            u, v, d = map(int, input().strip().split())
            edges.append((u, v, d))
        
        queries = []
        for _ in range(m):
            x, y = map(int, input().strip().split())
            queries.append((x, y))
        
        # 计算结果
        results = longest_path_through_two_nodes(n, edges, queries)
        
        # 输出结果
        for res in results:
            print(res)
    except Exception as e:
        print(f"发生错误: {e}", file=sys.stderr) 