def count_palindromes(s):
    """
    计算字符串中长度大于1的回文子串数量
    
    Args:
        s: 输入字符串
    
    Returns:
        int: 长度大于1的回文子串数量
    """
    n = len(s)
    count = 0
    
    # 检查所有可能的子串
    for i in range(n):
        for j in range(i + 1, n):  # j从i+1开始，确保长度至少为2
            substring = s[i:j+1]
            # 检查是否为回文
            if substring == substring[::-1]:
                count += 1
    
    return count


def is_palindrome(s):
    """
    检查字符串是否为回文
    
    Args:
        s: 输入字符串
    
    Returns:
        bool: 是否为回文
    """
    return s == s[::-1]


def count_palindromes_optimized(s):
    """
    优化版本：使用中心扩展法计算回文子串数量
    
    Args:
        s: 输入字符串
    
    Returns:
        int: 长度大于1的回文子串数量
    """
    n = len(s)
    count = 0
    
    # 以每个字符为中心扩展（奇数长度回文）
    for center in range(n):
        left, right = center - 1, center + 1
        while left >= 0 and right < n and s[left] == s[right]:
            count += 1  # 找到一个长度大于1的回文
            left -= 1
            right += 1
    
    # 以每两个字符之间为中心扩展（偶数长度回文）
    for center in range(n - 1):
        left, right = center, center + 1
        while left >= 0 and right < n and s[left] == s[right]:
            count += 1  # 找到一个长度大于1的回文
            left -= 1
            right += 1
    
    return count


def main():
    """
    主函数：处理输入输出
    """
    # 读取输入
    s = input().strip()
    
    # 计算结果
    result = count_palindromes(s)
    
    # 输出结果
    print(result)


if __name__ == "__main__":
    # 测试用例
    test_cases = [
        "AHHAMTT",  # 示例输入
        "ABBA",     # 简单回文
        "AABBAA",   # 多个回文
        "ABC",      # 无回文
        "AA",       # 单个回文
    ]
    
    print("测试结果：")
    for test in test_cases:
        result1 = count_palindromes(test)
        result2 = count_palindromes_optimized(test)
        print(f"输入: {test}")
        print(f"方法1结果: {result1}")
        print(f"方法2结果: {result2}")
        print(f"验证: {'✓' if result1 == result2 else '✗'}")
        print("-" * 30)
    
    print("\n运行主程序（输入字符串）：")
    main()
